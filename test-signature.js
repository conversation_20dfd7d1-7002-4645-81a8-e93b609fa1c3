/**
 * 测试签名生成算法
 * 对比我们的实现与官方文档中的示例
 */

const crypto = require('crypto');

// 从文档中的示例数据
const testCases = [
  {
    name: "generateId API 示例",
    timestamp: 1755239241,
    nonce: "-un-m0ntXQIf0i-byz12f",
    deviceId: "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm",
    data: { timestamp: 1755239241456 },
    expectedSign: "03A7FFE5DCFB0AC2C486A05ED8198142"
  },
  {
    name: "createConversation API 示例",
    timestamp: 1755239240,
    nonce: "OOZpusZl8ANtYIAXqUkgP",
    deviceId: "eb845b952111b886e87bb092b2f718b8_3moaojk7xKMmLdud9MBm",
    data: {
      conversationList: [{
        metaData: {
          chatModelConfig: {},
          superAgentPath: "/chat"
        },
        shareId: "",
        isAnonymous: false,
        source: ""
      }]
    },
    expectedSign: "0D2DA56E3D33440213FCC5B1326C959B"
  }
];

/**
 * 我们当前的签名生成算法
 */
function generateSignature(params) {
  // 构建签名字符串的基础参数
  const signParams = {
    timestamp: params.timestamp,
    nonce: params.nonce,
    deviceId: params.deviceId,
  };

  // 如果有请求体数据，将其序列化后加入签名参数
  if (params.data) {
    signParams['data'] = JSON.stringify(params.data);
  }

  // 按键名排序并构建签名字符串
  const sortedKeys = Object.keys(signParams).sort();
  const signString = sortedKeys
    .map(key => `${key}=${signParams[key]}`)
    .join('&');

  console.log('签名字符串:', signString);

  // 生成MD5哈希并转为大写
  return crypto.createHash('md5')
    .update(signString)
    .digest('hex')
    .toUpperCase();
}

/**
 * 测试不同的签名算法变体
 */
function testSignatureVariants(testCase) {
  console.log(`\n=== 测试 ${testCase.name} ===`);
  console.log('期望签名:', testCase.expectedSign);

  // 变体1: 当前算法
  const sign1 = generateSignature(testCase);
  console.log('变体1 (当前):', sign1);
  console.log('匹配:', sign1 === testCase.expectedSign);

  // 变体2: 不包含data字段
  const params2 = {
    timestamp: testCase.timestamp,
    nonce: testCase.nonce,
    deviceId: testCase.deviceId
  };
  const signString2 = Object.keys(params2).sort()
    .map(key => `${key}=${params2[key]}`)
    .join('&');
  const sign2 = crypto.createHash('md5').update(signString2).digest('hex').toUpperCase();
  console.log('变体2 (无data):', sign2);
  console.log('匹配:', sign2 === testCase.expectedSign);

  // 变体3: 只使用timestamp+nonce+deviceId，不排序
  const signString3 = `timestamp=${testCase.timestamp}&nonce=${testCase.nonce}&deviceId=${testCase.deviceId}`;
  const sign3 = crypto.createHash('md5').update(signString3).digest('hex').toUpperCase();
  console.log('变体3 (固定顺序):', sign3);
  console.log('匹配:', sign3 === testCase.expectedSign);

  // 变体4: 尝试不同的连接符
  const signString4 = `${testCase.timestamp}${testCase.nonce}${testCase.deviceId}`;
  const sign4 = crypto.createHash('md5').update(signString4).digest('hex').toUpperCase();
  console.log('变体4 (无分隔符):', sign4);
  console.log('匹配:', sign4 === testCase.expectedSign);

  // 变体5: 尝试包含data但使用不同格式
  if (testCase.data) {
    const dataStr = JSON.stringify(testCase.data);
    const signString5 = `timestamp=${testCase.timestamp}&nonce=${testCase.nonce}&deviceId=${testCase.deviceId}&data=${dataStr}`;
    const sign5 = crypto.createHash('md5').update(signString5).digest('hex').toUpperCase();
    console.log('变体5 (固定顺序+data):', sign5);
    console.log('匹配:', sign5 === testCase.expectedSign);
  }

  // 变体6: 尝试使用URL编码
  if (testCase.data) {
    const dataStr = encodeURIComponent(JSON.stringify(testCase.data));
    const signString6 = `data=${dataStr}&deviceId=${testCase.deviceId}&nonce=${testCase.nonce}&timestamp=${testCase.timestamp}`;
    const sign6 = crypto.createHash('md5').update(signString6).digest('hex').toUpperCase();
    console.log('变体6 (URL编码):', sign6);
    console.log('匹配:', sign6 === testCase.expectedSign);
  }

  // 变体7: 尝试不同的哈希算法
  const signString7 = Object.keys(params2).sort()
    .map(key => `${key}=${params2[key]}`)
    .join('&');
  const sign7 = crypto.createHash('sha1').update(signString7).digest('hex').toUpperCase();
  console.log('变体7 (SHA1):', sign7);
  console.log('匹配:', sign7 === testCase.expectedSign);
}

// 运行测试
console.log('开始测试签名生成算法...\n');

testCases.forEach(testCase => {
  testSignatureVariants(testCase);
});

console.log('\n=== 测试完成 ===');
