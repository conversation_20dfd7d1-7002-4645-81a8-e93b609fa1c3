# 当贝AI Provider SDK

一个完整的当贝AI API调用SDK，提供简洁易用的TypeScript接口来访问当贝AI的对话功能。

## 功能特性

- 🚀 **完整的API封装** - 支持对话创建、消息发送、流式响应等所有功能
- 🔐 **自动签名验证** - 内置请求签名生成和验证机制
- 📱 **设备管理** - 自动生成和管理设备标识
- 🌊 **流式响应** - 支持Server-Sent Events实时消息流
- 🛡️ **错误处理** - 完善的错误处理和重试机制
- 📝 **TypeScript支持** - 完整的类型定义和智能提示
- 🧪 **测试覆盖** - 全面的单元测试和集成测试

## 安装

```bash
npm install dangbei-provider
```

## 快速开始

### 基础用法

```typescript
import { DangbeiProvider } from 'dangbei-provider';

// 创建Provider实例
const provider = new DangbeiProvider({
  debug: true // 开启调试日志
});

// 快速聊天（自动创建对话）
async function quickChat() {
  try {
    const response = await provider.quickChat('你好，请介绍一下自己');
    console.log('AI回复:', response.content);
  } catch (error) {
    console.error('聊天失败:', error);
  }
}

quickChat();
```

### 流式聊天

```typescript
import { DangbeiProvider, ChatCallbacks } from 'dangbei-provider';

const provider = new DangbeiProvider();

async function streamChat() {
  // 创建对话
  const conversation = await provider.createConversation();

  // 设置流式回调
  const callbacks: ChatCallbacks = {
    onMessage: (content, data) => {
      // 实时接收消息片段
      process.stdout.write(content);
    },
    onComplete: (data) => {
      console.log('\n聊天完成，消息ID:', data.id);
    },
    onError: (error) => {
      console.error('聊天错误:', error);
    }
  };

  // 发送消息
  await provider.chat({
    conversationId: conversation.conversationId,
    question: '请详细介绍一下人工智能的发展历史',
    callbacks
  });
}

streamChat();
```

## API文档

### DangbeiProvider

主要的Provider类，提供所有API功能。

#### 构造函数

```typescript
new DangbeiProvider(options?: DangbeiProviderOptions)
```

**参数:**
- `options.deviceConfig` - 设备配置（可选）
- `options.timeout` - 请求超时时间，默认30000ms
- `options.retries` - 重试次数，默认3次
- `options.debug` - 是否开启调试日志，默认false

#### 主要方法

##### createConversation(options?)

创建新的对话会话。

```typescript
const conversation = await provider.createConversation({
  superAgentPath: '/chat', // 可选，默认'/chat'
  isAnonymous: false,      // 可选，默认false
  source: ''               // 可选，来源标识
});
```

##### chat(options)

发送聊天消息并接收流式响应。

```typescript
const response = await provider.chat({
  conversationId: 'your-conversation-id',
  question: '你的问题',
  model: 'doubao-1_6-thinking', // 可选，默认模型
  callbacks: {
    onMessage: (content, data) => console.log(content),
    onComplete: (data) => console.log('完成'),
    onError: (error) => console.error(error)
  }
});
```

##### chatSync(options)

发送聊天消息并等待完整响应。

```typescript
const content = await provider.chatSync({
  conversationId: 'your-conversation-id',
  question: '你的问题'
});
```

##### quickChat(question, callbacks?)

快速聊天，自动创建对话并发送消息。

```typescript
const response = await provider.quickChat('你好');
```

##### generateId(timestamp?)

生成唯一ID。

```typescript
const id = await provider.generateId();
```

##### 其他方法

- `stopChat()` - 停止当前聊天
- `getDeviceConfig()` - 获取设备配置
- `updateDeviceConfig(config)` - 更新设备配置
- `getStatus()` - 获取服务状态
- `checkServiceAvailability()` - 检查服务可用性
- `destroy()` - 销毁实例

## 配置选项

### DeviceConfig

设备配置选项：

```typescript
interface DeviceConfig {
  deviceId: string;      // 设备ID
  appType: number;       // 应用类型，固定为6
  appVersion: string;    // 应用版本
  clientVersion: string; // 客户端版本
  lang: string;          // 语言，默认'zh'
  userAgent: string;     // 用户代理
}
```

### ChatOptions

聊天配置选项：

```typescript
interface ChatOptions {
  conversationId: string;           // 对话ID（必需）
  question: string;                 // 问题内容（必需）
  model?: string;                   // 模型名称
  botCode?: string;                 // 机器人代码
  chatOption?: {                    // 聊天选项
    searchKnowledge?: boolean;
    searchAllKnowledge?: boolean;
    searchSharedKnowledge?: boolean;
  };
  callbacks?: ChatCallbacks;        // 回调函数
}
```

## 错误处理

SDK提供了完善的错误处理机制：

```typescript
import { DangbeiApiError, ErrorType } from 'dangbei-provider';

try {
  await provider.chat(options);
} catch (error) {
  if (error instanceof DangbeiApiError) {
    console.log('错误类型:', error.type);
    console.log('错误代码:', error.code);
    console.log('请求ID:', error.requestId);

    switch (error.type) {
      case ErrorType.NETWORK_ERROR:
        console.log('网络连接失败');
        break;
      case ErrorType.API_ERROR:
        console.log('API调用失败');
        break;
      case ErrorType.PARAMETER_ERROR:
        console.log('参数错误');
        break;
      // ... 其他错误类型
    }
  }
}
```

## 高级用法

### 自定义设备配置

```typescript
const provider = new DangbeiProvider({
  deviceConfig: {
    deviceId: 'your-custom-device-id',
    lang: 'en',
    appVersion: '2.0.0'
  }
});
```

### 使用底层服务

```typescript
import {
  HttpClient,
  ConversationService,
  ChatService,
  DeviceUtils
} from 'dangbei-provider';

// 创建自定义HTTP客户端
const httpClient = new HttpClient({
  deviceId: DeviceUtils.generateDeviceId()
});

// 使用底层服务
const conversationService = new ConversationService(httpClient);
const chatService = new ChatService(httpClient);
```

## 开发和测试

### 安装依赖

```bash
npm install
```

### 运行测试

```bash
# 运行所有测试
npm test

# 运行测试并监听变化
npm run test:watch

# 运行特定测试
npm test -- --testNamePattern="Provider"
```

### 构建项目

```bash
npm run build
```

### 代码检查

```bash
npm run lint
npm run lint:fix
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0

- 初始版本发布
- 支持完整的当贝AI API调用
- 提供TypeScript类型定义
- 包含完整的测试套件