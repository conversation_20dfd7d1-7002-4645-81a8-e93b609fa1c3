/**
 * 签名生成工具
 * 用于生成当贝AI API请求所需的安全签名
 */

import { createHash } from 'crypto';
import { SignatureParams } from '../types';

/**
 * 签名生成器类
 * 负责生成API请求的MD5签名
 */
export class SignatureUtils {
  /**
   * 生成API请求签名
   * 尝试多种签名算法以匹配官方实现
   *
   * @param params 签名参数
   * @returns MD5签名字符串（大写）
   */
  public static generateSignature(params: SignatureParams): string {
    // 尝试方法1: 只使用基础参数，不包含data（基于测试结果调整）
    const basicParams = {
      timestamp: params.timestamp,
      nonce: params.nonce,
      deviceId: params.deviceId,
    };

    // 按键名排序并构建签名字符串
    const sortedKeys = Object.keys(basicParams).sort();
    const signString = sortedKeys
      .map(key => `${key}=${basicParams[key as keyof typeof basicParams]}`)
      .join('&');

    // 生成标准MD5签名
    const signature = createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();

    // 在调试模式下输出详细信息
    if (process.env['NODE_ENV'] === 'development') {
      console.log('🔐 签名生成详情:');
      console.log('  - 签名字符串:', signString);
      console.log('  - 生成的签名:', signature);
      if (params.data) {
        console.log('  - 请求数据:', JSON.stringify(params.data));
      }
      console.log('  - 注意: 当前签名算法可能与官方不匹配，API调用可能失败');
    }

    return signature;
  }

  /**
   * 生成随机nonce字符串
   * 
   * @param length nonce长度，默认为21
   * @returns 随机字符串
   */
  public static generateNonce(length: number = 21): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * 获取当前时间戳（秒）
   * 
   * @returns Unix时间戳
   */
  public static getTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 获取当前时间戳（毫秒）
   * 
   * @returns Unix时间戳（毫秒）
   */
  public static getTimestampMs(): number {
    return Date.now();
  }

  /**
   * 验证签名是否有效
   * 
   * @param signature 待验证的签名
   * @param params 签名参数
   * @returns 是否有效
   */
  public static verifySignature(signature: string, params: SignatureParams): boolean {
    const expectedSignature = this.generateSignature(params);
    return signature.toUpperCase() === expectedSignature;
  }
}
