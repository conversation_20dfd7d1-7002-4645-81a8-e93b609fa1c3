/**
 * 签名生成工具
 * 用于生成当贝AI API请求所需的安全签名
 */

import { createHash } from 'crypto';
import { SignatureParams } from '../types';

/**
 * 签名生成器类
 * 负责生成API请求的MD5签名
 */
export class SignatureUtils {
  /**
   * 生成API请求签名
   *
   * 注意：经过详尽的逆向工程分析，我们无法完全匹配当贝AI的官方签名算法。
   * 当贝AI可能使用了专有的加密算法、服务器端密钥或其他动态参数。
   * 当前实现提供了一个标准的签名生成方式，但API调用可能会失败。
   * 程序设计了完善的错误处理和本地备用方案来应对这种情况。
   *
   * @param params 签名参数
   * @returns MD5签名字符串（大写）
   */
  public static generateSignature(params: SignatureParams): string {
    // 使用标准的参数排序方式生成签名
    // 基于对真实API请求的分析，我们尝试最接近的算法
    const basicParams = {
      timestamp: params.timestamp,
      nonce: params.nonce,
      deviceId: params.deviceId,
    };

    // 按字母顺序排序参数键名
    const sortedKeys = Object.keys(basicParams).sort();
    const signString = sortedKeys
      .map(key => `${key}=${basicParams[key as keyof typeof basicParams]}`)
      .join('&');

    // 生成MD5哈希签名（大写格式，符合观察到的格式）
    const signature = createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();

    // 在调试模式下输出详细的签名信息
    if (process.env['NODE_ENV'] === 'development') {
      console.log('🔐 签名生成详情:');
      console.log('  - 算法类型: 标准MD5哈希');
      console.log('  - 签名字符串:', signString);
      console.log('  - 生成的签名:', signature);
      console.log('  - 参数数量:', sortedKeys.length);
      if (params.data) {
        console.log('  - 请求数据:', JSON.stringify(params.data));
        console.log('  - 注意: 请求数据未包含在签名中');
      }
      console.log('  - ⚠️  警告: 此签名可能与官方算法不匹配');
      console.log('  - 📋 备用方案: API失败时将使用本地生成的ID');
    }

    return signature;
  }

  /**
   * 生成随机nonce字符串
   * 
   * @param length nonce长度，默认为21
   * @returns 随机字符串
   */
  public static generateNonce(length: number = 21): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * 获取当前时间戳（秒）
   * 
   * @returns Unix时间戳
   */
  public static getTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 获取当前时间戳（毫秒）
   * 
   * @returns Unix时间戳（毫秒）
   */
  public static getTimestampMs(): number {
    return Date.now();
  }

  /**
   * 验证签名是否有效
   * 
   * @param signature 待验证的签名
   * @param params 签名参数
   * @returns 是否有效
   */
  public static verifySignature(signature: string, params: SignatureParams): boolean {
    const expectedSignature = this.generateSignature(params);
    return signature.toUpperCase() === expectedSignature;
  }
}
