/**
 * 签名生成工具
 * 用于生成当贝AI API请求所需的安全签名
 */

import { createHash } from 'crypto';
import { SignatureParams } from '../types';

/**
 * 签名生成器类
 * 负责生成API请求的MD5签名
 */
export class SignatureUtils {
  /**
   * 生成API请求签名
   * 
   * @param params 签名参数
   * @returns MD5签名字符串（大写）
   */
  public static generateSignature(params: SignatureParams): string {
    // 构建签名字符串的基础参数
    const signParams: Record<string, string | number> = {
      timestamp: params.timestamp,
      nonce: params.nonce,
      deviceId: params.deviceId,
    };

    // 如果有请求体数据，将其序列化后加入签名参数
    if (params.data) {
      signParams['data'] = JSON.stringify(params.data);
    }

    // 按键名排序并构建签名字符串
    const sortedKeys = Object.keys(signParams).sort();
    const signString = sortedKeys
      .map(key => `${key}=${signParams[key]}`)
      .join('&');

    // 生成MD5哈希并转为大写
    return createHash('md5')
      .update(signString)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 生成随机nonce字符串
   * 
   * @param length nonce长度，默认为21
   * @returns 随机字符串
   */
  public static generateNonce(length: number = 21): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * 获取当前时间戳（秒）
   * 
   * @returns Unix时间戳
   */
  public static getTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 获取当前时间戳（毫秒）
   * 
   * @returns Unix时间戳（毫秒）
   */
  public static getTimestampMs(): number {
    return Date.now();
  }

  /**
   * 验证签名是否有效
   * 
   * @param signature 待验证的签名
   * @param params 签名参数
   * @returns 是否有效
   */
  public static verifySignature(signature: string, params: SignatureParams): boolean {
    const expectedSignature = this.generateSignature(params);
    return signature.toUpperCase() === expectedSignature;
  }
}
