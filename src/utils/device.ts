/**
 * 设备相关工具
 * 用于生成设备ID和管理设备配置信息
 */

import { createHash } from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import { DeviceConfig } from '../types';

/**
 * 设备工具类
 * 负责生成和管理设备相关信息
 */
export class DeviceUtils {
  /**
   * 生成设备ID
   * 格式：{hash}_{randomSuffix}
   * 
   * @param seed 种子字符串，用于生成一致的设备ID
   * @returns 设备ID字符串
   */
  public static generateDeviceId(seed?: string): string {
    // 如果没有提供种子，使用随机UUID
    const baseSeed = seed || uuidv4();
    
    // 生成基础哈希
    const hash = createHash('md5')
      .update(baseSeed)
      .digest('hex');
    
    // 生成随机后缀
    const randomSuffix = this.generateRandomString(16);
    
    return `${hash}_${randomSuffix}`;
  }

  /**
   * 生成随机字符串
   * 
   * @param length 字符串长度
   * @returns 随机字符串
   */
  public static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * 创建默认设备配置
   * 
   * @param deviceId 设备ID，如果不提供则自动生成
   * @returns 设备配置对象
   */
  public static createDefaultDeviceConfig(deviceId?: string): DeviceConfig {
    return {
      deviceId: deviceId || this.generateDeviceId(),
      appType: 6,
      appVersion: '1.1.17-22',
      clientVersion: '1.0.2',
      lang: 'zh',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0'
    };
  }

  /**
   * 验证设备ID格式
   * 
   * @param deviceId 设备ID
   * @returns 是否为有效格式
   */
  public static isValidDeviceId(deviceId: string): boolean {
    // 检查基本格式：hash_suffix
    const parts = deviceId.split('_');
    if (parts.length !== 2) {
      return false;
    }

    const [hash, suffix] = parts;

    // 验证哈希部分（32位十六进制）
    if (!hash || !/^[a-f0-9]{32}$/i.test(hash)) {
      return false;
    }

    // 验证后缀部分（字母数字组合）
    if (!suffix || !/^[A-Za-z0-9]+$/.test(suffix)) {
      return false;
    }

    return true;
  }

  /**
   * 获取标准请求头
   * 
   * @param config 设备配置
   * @param additionalHeaders 额外的请求头
   * @returns 请求头对象
   */
  public static getStandardHeaders(
    config: DeviceConfig,
    additionalHeaders: Record<string, string> = {}
  ): Record<string, string> {
    const baseHeaders = {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
      'Connection': 'keep-alive',
      'Origin': 'https://ai.dangbei.com',
      'Referer': 'https://ai.dangbei.com/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site',
      'User-Agent': config.userAgent,
      'appType': config.appType.toString(),
      'appVersion': config.appVersion,
      'client-ver': config.clientVersion,
      'content-type': 'application/json',
      'deviceId': config.deviceId,
      'lang': config.lang,
      'sec-ch-ua': '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'token': '' // 通常为空
    };

    return { ...baseHeaders, ...additionalHeaders };
  }
}
